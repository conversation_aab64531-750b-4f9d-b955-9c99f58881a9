/**
 * Custom String Compression Module
 * 
 * This module provides custom string compression functionality that replaces
 * js-confuser's stringCompression option. It uses Babel to find strings and
 * LZ-String for compression, with completely randomized variable names.
 */

const babel = require('@babel/core');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');
const LZString = require('lz-string');
const { generateRandomName } = require('./utils');

/**
 * Generate a completely random and non-descriptive variable name
 * @returns {string} Random variable name
 */
function generateObfuscatedName() {
    return generateRandomName(Math.floor(Math.random() * 8) + 6);
}

/**
 * Check if a string literal is part of a module import/require
 * @param {Object} path - Babel path object
 * @returns {boolean} True if it's a module import
 */
function isModuleImport(path) {
    const parent = path.parent;
    
    // Check for require() calls
    if (t.isCallExpression(parent) && 
        t.isIdentifier(parent.callee, { name: 'require' }) &&
        parent.arguments[0] === path.node) {
        return true;
    }
    
    // Check for import statements
    if (t.isImportDeclaration(path.findParent(p => p.isImportDeclaration())?.node)) {
        return true;
    }
    
    // Check for export statements
    if (t.isExportNamedDeclaration(path.findParent(p => p.isExportNamedDeclaration())?.node) ||
        t.isExportDefaultDeclaration(path.findParent(p => p.isExportDefaultDeclaration())?.node)) {
        return true;
    }
    
    return false;
}

/**
 * Ensure the path can be replaced with a computed expression
 * @param {Object} path - Babel path object
 */
function ensureComputedExpression(path) {
    const parent = path.parent;
    
    // If it's a property key in object notation, convert to computed
    if (t.isObjectProperty(parent) && parent.key === path.node && !parent.computed) {
        parent.computed = true;
    }
    
    // If it's a member expression property, convert to computed
    if (t.isMemberExpression(parent) && parent.property === path.node && !parent.computed) {
        parent.computed = true;
    }
}

/**
 * LZ-String library minified code with randomized variable names
 */
const StringCompressionLibraryMinified = `
var {StringCompressionLibrary}=function(){var r=String.fromCharCode,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",e={};function t(r,o){if(!e[r]){e[r]={};for(var n=0;n<r.length;n++)e[r][r.charAt(n)]=n}return e[r][o]}var i={compressToBase64:function(r){if(null==r)return"";var n=i._compress(r,6,function(r){return o.charAt(r)});switch(n.length%4){default:case 0:return n;case 1:return n+"===";case 2:return n+"==";case 3:return n+"="}},decompressFromBase64:function(r){return null==r?"":""==r?null:i._decompress(r.length,32,function(n){return t(o,r.charAt(n))})},compressToUTF16:function(o){return null==o?"":i._compress(o,15,function(o){return r(o+32)})+" "},decompressFromUTF16:function(r){return null==r?"":""==r?null:i._decompress(r.length,16384,function(o){return r.charCodeAt(o)-32})},compressToUint8Array:function(r){for(var o=i.compress(r),n=new Uint8Array(2*o.length),e=0,t=o.length;e<t;e++){var s=o.charCodeAt(e);n[2*e]=s>>>8,n[2*e+1]=s%256}return n},decompressFromUint8Array:function(o){if(null==o)return i.decompress(o);for(var n=new Array(o.length/2),e=0,t=n.length;e<t;e++)n[e]=256*o[2*e]+o[2*e+1];var s=[];return n.forEach(function(o){s.push(r(o))}),i.decompress(s.join(""))},compressToEncodedURIComponent:function(r){return null==r?"":i._compress(r,6,function(r){return n.charAt(r)})},decompressFromEncodedURIComponent:function(r){return null==r?"":""==r?null:(r=r.replace(/ /g,"+"),i._decompress(r.length,32,function(o){return t(n,r.charAt(o))}))},compress:function(o){return i._compress(o,16,function(o){return r(o)})},_compress:function(r,o,n){if(null==r)return"";var e,t,i,s={},u={},a="",p="",c="",l=2,f=3,h=2,d=[],m=0,v=0;for(i=0;i<r.length;i+=1)if(a=r.charAt(i),Object.prototype.hasOwnProperty.call(s,a)||(s[a]=f++,u[a]=!0),p=c+a,Object.prototype.hasOwnProperty.call(s,p))c=p;else{if(Object.prototype.hasOwnProperty.call(u,c)){if(c.charCodeAt(0)<256){for(e=0;e<h;e++)m<<=1,v==o-1?(v=0,d.push(n(m)),m=0):v++;for(t=c.charCodeAt(0),e=0;e<8;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1}else{for(t=1,e=0;e<h;e++)m=m<<1|t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t=0;for(t=c.charCodeAt(0),e=0;e<16;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1}0==--l&&(l=Math.pow(2,h),h++),delete u[c]}else for(t=s[c],e=0;e<h;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1;0==--l&&(l=Math.pow(2,h),h++),s[p]=f++,c=String(a)}if(""!==c){if(Object.prototype.hasOwnProperty.call(u,c)){if(c.charCodeAt(0)<256){for(e=0;e<h;e++)m<<=1,v==o-1?(v=0,d.push(n(m)),m=0):v++;for(t=c.charCodeAt(0),e=0;e<8;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1}else{for(t=1,e=0;e<h;e++)m=m<<1|t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t=0;for(t=c.charCodeAt(0),e=0;e<16;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1}0==--l&&(l=Math.pow(2,h),h++),delete u[c]}else for(t=s[c],e=0;e<h;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1;0==--l&&(l=Math.pow(2,h),h++)}for(t=2,e=0;e<h;e++)m=m<<1|1&t,v==o-1?(v=0,d.push(n(m)),m=0):v++,t>>=1;for(;;){if(m<<=1,v==o-1){d.push(n(m));break}v++}return d.join("")},decompress:function(r){return null==r?"":""==r?null:i._decompress(r.length,32768,function(o){return r.charCodeAt(o)})},_decompress:function(o,n,e){var t,i,s,u,a,p,c,l=[],f=4,h=4,d=3,m="",v=[],g={val:e(0),position:n,index:1};for(t=0;t<3;t+=1)l[t]=t;for(s=0,a=Math.pow(2,2),p=1;p!=a;)u=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=e(g.index++)),s|=(u>0?1:0)*p,p<<=1;switch(s){case 0:for(s=0,a=Math.pow(2,8),p=1;p!=a;)u=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=e(g.index++)),s|=(u>0?1:0)*p,p<<=1;c=r(s);break;case 1:for(s=0,a=Math.pow(2,16),p=1;p!=a;)u=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=e(g.index++)),s|=(u>0?1:0)*p,p<<=1;c=r(s);break;case 2:return""}for(l[3]=c,i=c,v.push(c);;){if(g.index>o)return"";for(s=0,a=Math.pow(2,d),p=1;p!=a;)u=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=e(g.index++)),s|=(u>0?1:0)*p,p<<=1;switch(c=s){case 0:for(s=0,a=Math.pow(2,8),p=1;p!=a;)u=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=e(g.index++)),s|=(u>0?1:0)*p,p<<=1;l[h++]=r(s),c=h-1,f--;break;case 1:for(s=0,a=Math.pow(2,16),p=1;p!=a;)u=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=e(g.index++)),s|=(u>0?1:0)*p,p<<=1;l[h++]=r(s),c=h-1,f--;break;case 2:return v.join("")}if(0==f&&(f=Math.pow(2,d),d++),l[c])m=l[c];else{if(c!==h)return null;m=i+i.charAt(0)}v.push(m),l[h++]=i+m.charAt(0),i=m,0==--f&&(f=Math.pow(2,d),d++)}}};return i}();"function"==typeof define&&define.amd?define(function(){return {StringCompressionLibrary}}):"undefined"!=typeof module&&null!=module?module.exports={StringCompressionLibrary}:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",function(){return {StringCompressionLibrary}});`;

/**
 * Create the string decompression template
 * @param {string} functionName - Name of the decompression function
 * @param {string} compressedString - The compressed string data
 * @param {string} stringDelimiter - Delimiter used to separate strings
 * @returns {string} Decompression code template
 */
function createDecompressionTemplate(functionName, compressedString, stringDelimiter, libraryVarName) {
    const arrayVarName = generateObfuscatedName();
    const tempVarName = generateObfuscatedName();
    const indexVarName = generateObfuscatedName();
    const decompressVarName = generateObfuscatedName();

    return `
var ${functionName};
(function() {
    var ${decompressVarName} = "${compressedString}";
    var ${tempVarName} = ${libraryVarName};
    var ${arrayVarName} = ${tempVarName}["decompressFromUTF16"](${decompressVarName})["split"]("${stringDelimiter}");
    ${functionName} = function(${indexVarName}) {
        return ${arrayVarName}[${indexVarName}];
    };
})();`;
}



/**
 * Process JavaScript code to compress strings
 * @param {string} code - The JavaScript code to process
 * @param {Object} options - Processing options
 * @returns {Object} Result object with processed code and metadata
 */
async function processStringCompression(code, options = {}) {
    try {
        const startTime = Date.now();
        const stringDelimiter = "|";
        const stringMap = new Map();
        let stringsProcessed = 0;

        // Generate single random function name for all string decompression
        const mainDecompressFnName = generateObfuscatedName();

        // Parse the code into AST
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        // Traverse AST to find and collect strings
        traverse(ast, {
            StringLiteral(path) {
                // Skip module imports
                if (isModuleImport(path)) {
                    return;
                }

                const originalValue = path.node.value;

                // Must be at least 3 characters long
                if (originalValue.length < 3) {
                    return;
                }

                // Cannot contain the string delimiter
                if (originalValue.includes(stringDelimiter)) {
                    return;
                }

                // Skip very short or single character strings
                if (originalValue.trim().length < 2) {
                    return;
                }

                let index = stringMap.get(originalValue);
                if (typeof index === 'undefined') {
                    index = stringMap.size;
                    stringMap.set(originalValue, index);
                }

                stringsProcessed++;

                // Ensure the string can be replaced with a computed expression
                ensureComputedExpression(path);

                // Replace the string literal with a call to the main decompression function
                path.replaceWith(
                    t.callExpression(
                        t.identifier(mainDecompressFnName),
                        [t.numericLiteral(index)]
                    )
                );
            }
        });
        
        // If no strings were processed, return original code
        if (stringMap.size === 0) {
            return {
                code: code,
                success: true,
                processingTime: Date.now() - startTime,
                stringsCompressed: 0,
                compressionRatio: 1.0,
                originalSize: Buffer.byteLength(code, 'utf8'),
                compressedSize: Buffer.byteLength(code, 'utf8')
            };
        }
        
        // Create string payload and compress it
        const stringPayload = Array.from(stringMap.keys()).join(stringDelimiter);
        const compressedString = LZString.compressToUTF16(stringPayload);
        
        // Generate random library variable name
        const stringCompressionLibraryName = generateObfuscatedName();

        // Create the LZ-String library injection with randomized name
        const lzStringLibraryCode = StringCompressionLibraryMinified.replace(
            /{StringCompressionLibrary}/g,
            stringCompressionLibraryName
        );

        // Create decompression template
        const decompressionCode = createDecompressionTemplate(
            mainDecompressFnName,
            compressedString,
            stringDelimiter,
            stringCompressionLibraryName
        );

        // Generate the final code
        const generatedCode = generate(ast, {
            compact: false,
            minified: false
        }).code;

        // Combine library, decompression function and processed code
        const finalCode = lzStringLibraryCode + '\n' + decompressionCode + '\n' + generatedCode;
        
        const processingTime = Date.now() - startTime;
        const originalSize = Buffer.byteLength(code, 'utf8');
        const finalSize = Buffer.byteLength(finalCode, 'utf8');
        
        return {
            code: finalCode,
            success: true,
            processingTime: processingTime,
            stringsCompressed: stringMap.size,
            stringsProcessed: stringsProcessed,
            compressionRatio: finalSize / originalSize,
            originalSize: originalSize,
            compressedSize: finalSize,
            metadata: {
                stringDelimiter: stringDelimiter,
                compressedStringLength: compressedString.length,
                originalStringPayloadLength: stringPayload.length,
                uniqueStrings: stringMap.size
            }
        };
        
    } catch (error) {
        console.error('String compression processing error:', error);
        
        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            processingTime: 0,
            stringsCompressed: 0,
            compressionRatio: 1.0,
            originalSize: Buffer.byteLength(code, 'utf8'),
            compressedSize: Buffer.byteLength(code, 'utf8')
        };
    }
}

module.exports = {
    processStringCompression,
    generateObfuscatedName,
    createDecompressionTemplate,
    StringCompressionLibraryMinified
};
